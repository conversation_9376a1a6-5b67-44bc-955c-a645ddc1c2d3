/**
 * Chat Input Component
 * Animated input field with send button and suggestions
 */

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Send, Mic, Paperclip, Smile } from 'lucide-react';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  isLoading: boolean;
  placeholder?: string;
}

const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  isLoading,
  placeholder = "Type your message...",
}) => {
  const [message, setMessage] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Suggested quick messages
  const suggestions = [
    "What courses do you offer?",
    "I want to book SEE Bridge Course",
    "Tell me about IELTS preparation",
    "Show me BBS and BBA courses",
    "I need help with CSIT entrance prep",
    "What are the course fees?",
  ];

  const [showSuggestions, setShowSuggestions] = useState(false);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [message]);

  // Typing detection
  useEffect(() => {
    if (message.length > 0) {
      setIsTyping(true);
      const timer = setTimeout(() => setIsTyping(false), 1000);
      return () => clearTimeout(timer);
    } else {
      setIsTyping(false);
    }
  }, [message]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && !isLoading) {
      onSendMessage(message.trim());
      setMessage('');
      setShowSuggestions(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    onSendMessage(suggestion);
    setShowSuggestions(false);
  };

  const inputVariants = {
    focused: {
      scale: 1.01,
      boxShadow: '0 0 0 3px rgba(147, 51, 234, 0.1), 0 10px 25px rgba(0, 0, 0, 0.1)',
      transition: { duration: 0.3 }
    },
    unfocused: {
      scale: 1,
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
      transition: { duration: 0.3 }
    },
    typing: {
      scale: 1.005,
      boxShadow: '0 0 0 2px rgba(147, 51, 234, 0.15), 0 8px 20px rgba(147, 51, 234, 0.1)',
      transition: { duration: 0.2 }
    },
  };

  const buttonVariants = {
    idle: { scale: 1, rotate: 0 },
    hover: { scale: 1.1, rotate: 5 },
    tap: { scale: 0.95, rotate: -5 },
  };

  const typingIndicatorVariants = {
    hidden: { opacity: 0, y: 5 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.3 }
    },
  };

  return (
    <div className="relative">
      {/* Suggestions */}
      <AnimatePresence>
        {showSuggestions && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="absolute bottom-full mb-2 left-0 right-0 bg-white rounded-lg shadow-lg border p-2 space-y-1"
          >
            <p className="text-xs text-gray-500 px-2 py-1">Quick suggestions:</p>
            {suggestions.map((suggestion, index) => (
              <motion.button
                key={suggestion}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
                onClick={() => handleSuggestionClick(suggestion)}
                className="w-full text-left px-2 py-1 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors"
              >
                {suggestion}
              </motion.button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Input Container */}
      <motion.form
        onSubmit={handleSubmit}
        variants={inputVariants}
        animate={isTyping ? 'typing' : (isFocused ? 'focused' : 'unfocused')}
        className="bg-white rounded-2xl border-2 border-gray-200 shadow-lg p-3 flex items-end space-x-3 hover:shadow-xl transition-all duration-300"
      >
        {/* Action Buttons (Left) */}
        <div className="flex space-x-1">
          <motion.button
            type="button"
            variants={buttonVariants}
            initial="idle"
            whileHover="hover"
            whileTap="tap"
            onClick={() => setShowSuggestions(!showSuggestions)}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <Smile className="h-4 w-4" />
          </motion.button>
          
          <motion.button
            type="button"
            variants={buttonVariants}
            initial="idle"
            whileHover="hover"
            whileTap="tap"
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <Paperclip className="h-4 w-4" />
          </motion.button>
        </div>

        {/* Text Input */}
        <div className="flex-1">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            placeholder={placeholder}
            disabled={isLoading}
            rows={1}
            className="w-full resize-none border-none outline-none bg-transparent text-gray-900 placeholder-gray-500 max-h-32"
          />
        </div>

        {/* Send Button */}
        <motion.button
          type="submit"
          variants={buttonVariants}
          initial="idle"
          whileHover={!isLoading && message.trim() ? "hover" : "idle"}
          whileTap={!isLoading && message.trim() ? "tap" : "idle"}
          disabled={isLoading || !message.trim()}
          className={`p-3 rounded-xl transition-all duration-300 ${
            message.trim() && !isLoading
              ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          }`}
        >
          {isLoading ? (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
              className="h-4 w-4 border-2 border-current border-t-transparent rounded-full"
            />
          ) : (
            <Send className="h-4 w-4" />
          )}
        </motion.button>

        {/* Voice Input Button */}
        <motion.button
          type="button"
          variants={buttonVariants}
          initial="idle"
          whileHover="hover"
          whileTap="tap"
          className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
        >
          <Mic className="h-4 w-4" />
        </motion.button>
      </motion.form>

      {/* Typing Indicator */}
      <AnimatePresence>
        {isTyping && message.length > 0 && (
          <motion.div
            variants={typingIndicatorVariants}
            initial="hidden"
            animate="visible"
            exit="hidden"
            className="flex items-center justify-center mt-2 space-x-1"
          >
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 0.6, repeat: Infinity, delay: 0 }}
              className="w-1 h-1 bg-purple-500 rounded-full"
            />
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 0.6, repeat: Infinity, delay: 0.2 }}
              className="w-1 h-1 bg-purple-500 rounded-full"
            />
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 0.6, repeat: Infinity, delay: 0.4 }}
              className="w-1 h-1 bg-purple-500 rounded-full"
            />
            <span className="text-xs text-purple-600 ml-2 font-medium">Typing...</span>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ChatInput;
