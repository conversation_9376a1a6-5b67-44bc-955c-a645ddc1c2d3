from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import uuid
import json
import asyncio
from typing import AsyncGenerator

from api.services.chat_service import create_chat_service
from core.security import get_tenant_info
from models.user import UserTenantDB
from utils import setup_colored_logging, log_info, log_error, log_success

# Setup logging
setup_colored_logging()

router = APIRouter(tags=["Chat"])


class ChatRequest(BaseModel):
    message: str


class ToolUsed(BaseModel):
    name: str
    description: str
    input: dict = {}
    output: str = ""

class ChatResponse(BaseModel):
    response: str
    thread_id: str
    user_id: str
    tools_used: list[ToolUsed] = []
    message_id: str  # Unique ID for this chat exchange


@router.post("/chat", response_model=ChatResponse)
async def chat(
    chat_request: ChatRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Chat endpoint that uses ChatService with current user context
    The user ID from the token is used as the thread ID for conversation continuity
    """
    try:
        # Create chat service with current user context (includes vector store, agents, tools)
        chat_service = create_chat_service(current_user)

        # Use user ID as thread ID for conversation continuity per user
        thread_id = str(current_user.user.id)
        user_id = str(current_user.user.id)

        log_info(f"Chat request from user {current_user.user.username} (ID: {user_id}, tenant: {current_user.tenant_id}): {chat_request.message[:50]}...")

        # Get response from chat service (automatically uses current user's vector store and agents)
        agent_response = chat_service.chat(chat_request.message, thread_id)

        log_success(f"Chat response generated for user {current_user.user.username}")

        # Handle both old string format and new dict format for backward compatibility
        if isinstance(agent_response, dict):
            response_text = agent_response.get("response", "")
            tools_used = [ToolUsed(**tool) for tool in agent_response.get("tools_used", [])]
        else:
            response_text = agent_response
            tools_used = []

        return ChatResponse(
            response=response_text,
            thread_id=thread_id,
            user_id=user_id,
            message_id=str(uuid.uuid4()),
            tools_used=tools_used
        )
        
    except Exception as e:
        log_error(f"Chat failed for user {current_user.user.username}", e)
        raise HTTPException(
            status_code=500,
            detail="Failed to process chat request"
        )


@router.post("/stream")
async def chat_stream(
    chat_request: ChatRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Streaming chat endpoint that returns response in chunks
    """
    try:
        # Create chat service with current user context
        chat_service = create_chat_service(current_user)

        # Use user ID as thread ID for conversation continuity per user
        thread_id = str(current_user.user.id)
        user_id = str(current_user.user.id)

        log_info(f"Streaming chat request from user {current_user.user.username}")

        async def generate_response() -> AsyncGenerator[str, None]:
            try:
                # Get response from chat service
                agent_response = chat_service.chat(chat_request.message, thread_id)

                # Handle both old string format and new dict format
                if isinstance(agent_response, dict):
                    response_text = agent_response.get("response", "")
                    tools_used = agent_response.get("tools_used", [])
                else:
                    response_text = agent_response
                    tools_used = []

                # Send initial metadata
                metadata = {
                    "type": "metadata",
                    "thread_id": thread_id,
                    "user_id": user_id,
                    "message_id": str(uuid.uuid4()),
                    "tools_used": tools_used
                }
                yield f"data: {json.dumps(metadata)}\n\n"

                # Stream the response text word by word
                words = response_text.split()
                for i, word in enumerate(words):
                    chunk_data = {
                        "type": "chunk",
                        "content": word + (" " if i < len(words) - 1 else ""),
                        "index": i
                    }
                    yield f"data: {json.dumps(chunk_data)}\n\n"
                    await asyncio.sleep(0.05)  # Small delay for streaming effect

                # Send completion signal
                completion_data = {
                    "type": "complete",
                    "full_response": response_text
                }
                yield f"data: {json.dumps(completion_data)}\n\n"

            except Exception as e:
                error_data = {
                    "type": "error",
                    "error": str(e)
                }
                yield f"data: {json.dumps(error_data)}\n\n"

        return StreamingResponse(
            generate_response(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream",
            }
        )

    except Exception as e:
        log_error(f"Streaming chat failed for user {current_user.user.username}", e)
        raise HTTPException(
            status_code=500,
            detail="Failed to process streaming chat request"
        )


@router.delete("/chat/clear")
async def clear_conversation(
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Clear conversation history for the current user from MongoDB memory
    This clears the server-side conversation memory. Frontend should also clear local storage.
    """
    try:
        log_info(f"Clearing conversation for user {current_user.user.username}")

        # Get tenant database and MongoDB memory

        # Clear conversation for this user's thread
        thread_id = str(current_user.user.id)

        # Clear from both checkpoints and checkpoint_writes collections
        checkpoints_collection = current_user.db["checkpoints"]
        checkpoint_writes_collection = current_user.db["checkpoint_writes"]

        # Count documents before deletion for logging
        checkpoints_count = checkpoints_collection.count_documents({"thread_id": thread_id})
        writes_count = checkpoint_writes_collection.count_documents({"thread_id": thread_id})
        total_count = checkpoints_count + writes_count

        # Delete all conversation checkpoints for this user from both collections
        checkpoints_result = checkpoints_collection.delete_many({"thread_id": thread_id})
        writes_result = checkpoint_writes_collection.delete_many({"thread_id": thread_id})

        total_deleted = checkpoints_result.deleted_count + writes_result.deleted_count

        if total_count == 0:
            log_info(f"No conversation history found for user {current_user.user.username} - nothing to clear")
            message = f"No conversation history found for user {current_user.user.username}"
        else:
            log_success(f"Cleared conversation for user {current_user.user.username}: {total_deleted}/{total_count} documents deleted (checkpoints: {checkpoints_result.deleted_count}, writes: {writes_result.deleted_count})")
            message = f"Conversation history cleared for user {current_user.user.username} ({total_deleted} documents deleted)"

        return {
            "status": "success",
            "message": message,
            "deleted_count": total_deleted,
            "details": {
                "checkpoints_deleted": checkpoints_result.deleted_count,
                "writes_deleted": writes_result.deleted_count
            },
            "thread_id": thread_id,
            "user_id": str(current_user.user.id)
        }

    except Exception as e:
        log_error(f"Clear conversation failed for user {current_user.user.username}", e)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to clear conversation: {str(e)}"
        )


@router.get("/chat/health")
async def chat_health_check():
    """
    Health check endpoint for chat service
    """
    return {
        "status": "healthy",
        "service": "chat",
        "message": "Chat service is running with ChatService architecture"
    }
